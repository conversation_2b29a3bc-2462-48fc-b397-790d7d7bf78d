import { RedAIMCPClient } from "./redai-mcp-client";

/**
 * <PERSON><PERSON> dụ sử dụng RedAI MCP Client
 * 
 * File này minh họa cách sử dụng RedAI MCP Client để tương tác với RedAI Model API
 * thông qua MCP protocol.
 */

async function main() {
  // Tạo instance của client
  const client = new RedAIMCPClient();
  
  try {
    console.log("🚀 Bắt đầu demo RedAI MCP Client...\n");

    // 1. Tạo API key cho authentication
    const apiKey = client.generateApiKey();
    console.log("🔑 API Key được tạo:", apiKey);

    // 2. Kết nối đến MCP server
    console.log("\n📡 Đang kết nối đến MCP server...");
    await client.connect(apiKey);
    console.log("✅ Kết nối thành công!\n");

    // 3. Kiểm tra trạng thái authentication
    console.log("🔐 Kiểm tra trạng thái authentication...");
    const authStatus = await client.checkAuthStatus();
    console.log("Auth Status:", authStatus);

    // 4. Lấy danh sách tools có sẵn
    console.log("\n🛠️ Lấy danh sách tools...");
    const tools = await client.listTools();
    if (tools.success) {
      console.log("Tools có sẵn:", tools.data.tools?.length || 0);
      tools.data.tools?.slice(0, 5).forEach((tool: any) => {
        console.log(`  - ${tool.name}: ${tool.description || 'Không có mô tả'}`);
      });
    }

    // 5. Lấy danh sách resources
    console.log("\n📚 Lấy danh sách resources...");
    const resources = await client.listResources();
    if (resources.success) {
      console.log("Resources có sẵn:", resources.data.resources?.length || 0);
    }

    // 6. Demo Blog Tools
    console.log("\n📝 === DEMO BLOG TOOLS ===");

    // Test blog summary
    const blogSummary = await client.callTool("get_blog_summary", {});
    console.log("Blog summary:", blogSummary.success ? "✅ Lấy thành công" : "❌ Thất bại");
    if (blogSummary.success) {
      console.log("Summary data:", blogSummary.data);
    }

    // Test blog stats calculation
    const blogStats = await client.callTool("calculate_blog_stats", {
      blogId: "test-blog-123",
      period: "month"
    });
    console.log("Blog stats:", blogStats.success ? "✅ Tính thành công" : "❌ Thất bại");

    // Test suggest blog tags
    const tagSuggestion = await client.callTool("suggest_blog_tags", {
      content: "This is a sample blog post about technology and AI",
      category: "technology"
    });
    console.log("Tag suggestions:", tagSuggestion.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // 7. Demo quản lý Models
    console.log("\n🤖 === DEMO QUẢN LÝ MODELS ===");
    
    // Lấy system models
    const systemModels = await client.getSystemModels({
      provider: "OPENAI",
      page: 1,
      limit: 5
    });
    console.log("System models:", systemModels.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // Lấy tổng quan models
    const modelSummary = await client.getModelSummary();
    console.log("Model summary:", modelSummary.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // Gợi ý model cho task
    const suggestion = await client.suggestModelForTask({
      task_type: "text_generation",
      budget: "medium"
    });
    console.log("Model suggestion:", suggestion.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // 8. Demo quản lý Datasets
    console.log("\n📊 === DEMO QUẢN LÝ DATASETS ===");
    
    // Tạo dataset mới
    const newDataset = await client.createDataset({
      name: "Demo Customer Service Dataset",
      description: "Dataset demo cho fine-tuning customer service model",
      trainFileUrl: "https://example.com/train.jsonl",
      validFileUrl: "https://example.com/valid.jsonl"
    });
    console.log("Tạo dataset:", newDataset.success ? "✅ Thành công" : "❌ Thất bại");
    if (!newDataset.success) {
      console.log("Lỗi:", newDataset.error);
    }

    // Lấy danh sách datasets
    const datasets = await client.getDatasets({
      page: 1,
      limit: 10
    });
    console.log("Danh sách datasets:", datasets.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // Lấy URL upload
    const uploadUrl = await client.getUploadUrl();
    console.log("Upload URL:", uploadUrl.success ? "✅ Lấy thành công" : "❌ Thất bại");

    // 9. Demo tính toán chi phí
    console.log("\n💰 === DEMO TÍNH TOÁN CHI PHÍ ===");
    
    const cost = await client.calculateFineTuneCost({
      training_tokens: 100000,
      provider: "OPENAI"
    });
    console.log("Chi phí fine-tuning:", cost.success ? "✅ Tính thành công" : "❌ Thất bại");
    if (cost.success) {
      console.log("Chi phí dự kiến:", cost.data);
    }

    // 10. Demo tạo fine-tuning job (nếu có dataset)
    console.log("\n🎯 === DEMO FINE-TUNING JOB ===");
    
    if (newDataset.success && newDataset.data?.result?.id) {
      const fineTuningJob = await client.createFineTuningJob({
        datasetId: newDataset.data.result.id,
        baseModel: "gpt-3.5-turbo",
        suffix: "demo-v1",
        hyperparameters: {
          n_epochs: 3,
          batch_size: 1,
          learning_rate_multiplier: 0.1
        }
      });
      console.log("Tạo fine-tuning job:", fineTuningJob.success ? "✅ Thành công" : "❌ Thất bại");
      if (!fineTuningJob.success) {
        console.log("Lỗi:", fineTuningJob.error);
      }
    } else {
      console.log("⏭️ Bỏ qua tạo fine-tuning job (không có dataset hợp lệ)");
    }

    console.log("\n🎉 Demo hoàn thành!");

  } catch (error) {
    console.error("❌ Lỗi trong quá trình demo:", error);
  } finally {
    // Đóng kết nối
    console.log("\n🔌 Đóng kết nối...");
    await client.disconnect();
    console.log("✅ Đã đóng kết nối thành công!");
  }
}

// Chạy demo
if (require.main === module) {
  main().catch(console.error);
}

export { main as runDemo };
