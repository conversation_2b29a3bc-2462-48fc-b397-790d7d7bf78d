{"name": "test-mcp", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "ts-node client.ts", "start:new": "ts-node redai-mcp-client.ts", "build": "tsc", "dev": "ts-node --watch redai-mcp-client.ts"}, "author": "", "license": "ISC", "description": "RedAI MCP Client - TypeScript client for connecting to RedAI Model MCP Server", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "better-sqlite3": "^11.10.0", "crypto": "^1.0.1", "uuid": "^11.1.0", "zod": "^3.22.4", "axios": "^1.6.0"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^5.8.3", "@types/node": "^20.0.0"}}