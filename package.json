{"name": "test-mcp", "version": "1.0.0", "main": "index.js", "scripts": {"test": "ts-node test-client.ts", "test:connection": "ts-node test-client.ts", "start": "ts-node client.ts", "start:new": "ts-node redai-mcp-client.ts", "example": "ts-node example-usage.ts", "build": "tsc", "dev": "ts-node --watch redai-mcp-client.ts"}, "author": "", "license": "ISC", "description": "RedAI MCP Client - TypeScript client for connecting to RedAI Model MCP Server", "dependencies": {"@modelcontextprotocol/sdk": "^1.12.0", "axios": "^1.10.0", "better-sqlite3": "^11.10.0", "crypto": "^1.0.1", "uuid": "^11.1.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.19.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}