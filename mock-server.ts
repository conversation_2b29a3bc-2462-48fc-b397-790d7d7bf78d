import express from 'express';
import cors from 'cors';

/**
 * Mock MCP Server để test RedAI MCP Client
 * 
 * Server n<PERSON>y mô phỏng các endpoint c<PERSON> bản của MCP protocol
 * để test client mà không cần server thực tế.
 */

const app = express();
const PORT = 8006;

// Middleware
app.use(cors());
app.use(express.json());

// Mock data
const mockTools = [
  {
    name: "update_bearer_token",
    description: "Update bearer token for authentication",
    inputSchema: {
      type: "object",
      properties: {
        bearer_token: { type: "string" }
      }
    }
  },
  {
    name: "check_auth_status",
    description: "Check authentication status",
    inputSchema: { type: "object", properties: {} }
  },
  {
    name: "post_user_key_llm",
    description: "Create new API key",
    inputSchema: {
      type: "object",
      properties: {
        name: { type: "string" },
        provider: { type: "string" },
        apiKey: { type: "string" }
      }
    }
  },
  {
    name: "get_user_key_llm",
    description: "Get list of API keys",
    inputSchema: { type: "object", properties: {} }
  },
  {
    name: "get_model_summary",
    description: "Get model summary",
    inputSchema: { type: "object", properties: {} }
  }
];

const mockResources = [
  {
    uri: "config://app",
    name: "App Configuration",
    description: "Application configuration settings"
  }
];

// MCP Protocol endpoints
app.post('/mcp', async (req, res) => {
  const { method, params } = req.body;
  
  console.log(`📨 Received MCP request: ${method}`);
  
  try {
    switch (method) {
      case 'initialize':
        res.json({
          jsonrpc: "2.0",
          id: req.body.id,
          result: {
            protocolVersion: "2024-11-05",
            capabilities: {
              tools: {},
              resources: {},
              prompts: {}
            },
            serverInfo: {
              name: "Mock RedAI MCP Server",
              version: "1.0.0"
            }
          }
        });
        break;

      case 'tools/list':
        res.json({
          jsonrpc: "2.0",
          id: req.body.id,
          result: {
            tools: mockTools
          }
        });
        break;

      case 'resources/list':
        res.json({
          jsonrpc: "2.0",
          id: req.body.id,
          result: {
            resources: mockResources
          }
        });
        break;

      case 'tools/call':
        const toolName = params?.name;
        console.log(`🔧 Tool called: ${toolName}`);
        
        // Mock responses for different tools
        let mockResult;
        switch (toolName) {
          case 'update_bearer_token':
            mockResult = {
              success: true,
              message: "Bearer token updated successfully"
            };
            break;
            
          case 'check_auth_status':
            mockResult = {
              authenticated: true,
              user: "test_user",
              permissions: ["read", "write"]
            };
            break;
            
          case 'post_user_key_llm':
            mockResult = {
              result: {
                id: "key_" + Date.now(),
                name: params?.arguments?.name || "Test Key",
                provider: params?.arguments?.provider || "OPENAI",
                status: "active",
                createdAt: new Date().toISOString()
              }
            };
            break;
            
          case 'get_user_key_llm':
            mockResult = {
              result: {
                data: [
                  {
                    id: "key_123",
                    name: "Test OpenAI Key",
                    provider: "OPENAI",
                    status: "active",
                    createdAt: "2024-01-01T00:00:00Z"
                  }
                ],
                pagination: {
                  page: 1,
                  limit: 10,
                  total: 1
                }
              }
            };
            break;
            
          case 'get_model_summary':
            mockResult = {
              result: {
                totalModels: 5,
                activeKeys: 2,
                providers: ["OPENAI", "ANTHROPIC"],
                summary: "You have 5 models available across 2 providers"
              }
            };
            break;
            
          default:
            mockResult = {
              success: true,
              message: `Mock response for tool: ${toolName}`,
              data: params?.arguments || {}
            };
        }

        res.json({
          jsonrpc: "2.0",
          id: req.body.id,
          result: {
            content: [
              {
                type: "text",
                text: JSON.stringify(mockResult, null, 2)
              }
            ]
          }
        });
        break;

      default:
        res.status(400).json({
          jsonrpc: "2.0",
          id: req.body.id,
          error: {
            code: -32601,
            message: `Method not found: ${method}`
          }
        });
    }
  } catch (error) {
    console.error('❌ Error handling request:', error);
    res.status(500).json({
      jsonrpc: "2.0",
      id: req.body.id,
      error: {
        code: -32603,
        message: "Internal server error"
      }
    });
  }
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    server: 'Mock RedAI MCP Server',
    timestamp: new Date().toISOString()
  });
});

// Start server
app.listen(PORT, '127.0.0.1', () => {
  console.log(`🚀 Mock MCP Server đang chạy tại http://127.0.0.1:${PORT}`);
  console.log(`📡 MCP endpoint: http://127.0.0.1:${PORT}/mcp`);
  console.log(`🏥 Health check: http://127.0.0.1:${PORT}/health`);
  console.log(`\n✅ Server sẵn sàng để test client!`);
  console.log(`\n🧪 Để test client, chạy lệnh:`);
  console.log(`   npm test`);
  console.log(`   npm run example`);
});

export default app;
