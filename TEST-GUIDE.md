# Test Guide - RedAI MCP Client

Hướng dẫn test RedAI MCP Client với Mock Server.

## 🔍 **Phân tích lỗi ECONNREFUSED**

### **Lỗi gốc:**
```
connect ECONNREFUSED 127.0.0.1:8006
```

### **Nguyên nhân:**
- ❌ **MCP Server chưa chạy** tại port 8006
- ❌ **Không có service nào lắng nghe** trên địa chỉ đó
- ✅ **Client hoạt động bình thường** (API key generation, error handling)

### **Giải pháp:**
Tôi đã tạo **Mock MCP Server** để test client mà không cần server thực tế.

## 🚀 **Cách test với Mock Server**

### **Bước 1: Khởi động Mock Server**

Mở terminal thứ nhất và chạy:
```bash
npm run mock-server
```

Bạn sẽ thấy:
```
🚀 Mock MCP Server đang chạy tại http://127.0.0.1:8006
📡 MCP endpoint: http://127.0.0.1:8006/mcp
🏥 Health check: http://127.0.0.1:8006/health

✅ Server sẵn sàng để test client!
```

### **Bước 2: Test Client**

Mở terminal thứ hai và chạy:
```bash
# Test kết nối cơ bản
npm test

# Hoặc test đầy đủ
npm run example
```

## 📋 **Mock Server Features**

Mock server mô phỏng các endpoint chính:

### **MCP Protocol Endpoints:**
- ✅ `initialize` - Khởi tạo kết nối MCP
- ✅ `tools/list` - Lấy danh sách tools
- ✅ `resources/list` - Lấy danh sách resources
- ✅ `tools/call` - Gọi tools

### **Mock Tools:**
- ✅ `update_bearer_token` - Cập nhật authentication
- ✅ `check_auth_status` - Kiểm tra trạng thái auth
- ✅ `post_user_key_llm` - Tạo API key
- ✅ `get_user_key_llm` - Lấy danh sách API keys
- ✅ `get_model_summary` - Tổng quan models

### **Mock Responses:**
Server trả về dữ liệu giả lập realistic để test client logic.

## 🧪 **Kết quả mong đợi**

### **Khi chạy `npm test`:**
```
🧪 Bắt đầu test kết nối MCP client...

1️⃣ Tạo API key...
✅ API Key: redai_xxxxx...

2️⃣ Đang kết nối đến MCP server...
Đang thử kết nối với Streamable HTTP transport...
✅ Kết nối thành công với Streamable HTTP transport

3️⃣ Kiểm tra trạng thái kết nối...
✅ Trạng thái kết nối: Đã kết nối

4️⃣ Kiểm tra authentication...
Auth Status: ✅ OK

5️⃣ Lấy danh sách tools...
✅ Tools có sẵn: 5
📋 Một số tools:
   1. update_bearer_token
   2. check_auth_status
   3. post_user_key_llm
   4. get_user_key_llm
   5. get_model_summary

6️⃣ Lấy danh sách resources...
✅ Resources có sẵn: 1
📚 Một số resources:
   1. config://app

7️⃣ Test utility functions...
Validate API key: ✅ OK
Model summary: ✅ OK
Model suggestion: ✅ OK

🎉 Test hoàn thành thành công!
✅ Client hoạt động bình thường và có thể kết nối với MCP server.
```

## 🔧 **Troubleshooting**

### **Lỗi: Port đã được sử dụng**
```
Error: listen EADDRINUSE: address already in use :::8006
```

**Giải pháp:**
```bash
# Tìm process đang sử dụng port 8006
netstat -ano | findstr :8006

# Kill process (thay PID bằng số thực tế)
taskkill /PID <PID> /F

# Hoặc đổi port trong mock-server.ts
const PORT = 8007; // Đổi sang port khác
```

### **Lỗi: Module not found**
```
Error: Cannot find module 'express'
```

**Giải pháp:**
```bash
npm install express cors @types/express @types/cors
```

### **Client vẫn báo ECONNREFUSED**
1. ✅ Đảm bảo mock server đang chạy
2. ✅ Kiểm tra port 8006 có đang listen không
3. ✅ Kiểm tra firewall/antivirus

## 🎯 **Workflow Test hoàn chỉnh**

### **Terminal 1 - Mock Server:**
```bash
npm run mock-server
# Giữ terminal này mở
```

### **Terminal 2 - Test Client:**
```bash
# Test cơ bản
npm test

# Test đầy đủ với demo
npm run example

# Test kết nối đơn giản
curl http://127.0.0.1:8006/health
```

## 📊 **So sánh kết quả**

| Trước (Lỗi) | Sau (Thành công) |
|--------------|------------------|
| ❌ ECONNREFUSED | ✅ Kết nối thành công |
| ❌ Transport failed | ✅ Streamable HTTP OK |
| ❌ No server | ✅ Mock server running |
| ❌ No tools | ✅ 5 tools available |
| ❌ No resources | ✅ 1 resource available |

## 🚀 **Next Steps**

1. **Test với Mock Server** - Đảm bảo client hoạt động
2. **Tích hợp Server thực** - Khi có RedAI Model MCP Server
3. **Customize Mock** - Thêm endpoints theo nhu cầu
4. **Production Ready** - Deploy client với server thực

Mock server này giúp bạn:
- ✅ **Verify client logic** mà không cần server thực
- ✅ **Debug transport issues** 
- ✅ **Test error handling**
- ✅ **Develop offline**

Happy testing! 🎉
