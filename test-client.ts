import { RedAIMCPClient } from "./redai-mcp-client";

/**
 * Test script đơn giản để kiểm tra kết nối MCP client
 */

async function testConnection() {
  const client = new RedAIMCPClient();
  
  try {
    console.log("🧪 Bắt đầu test kết nối MCP client...\n");

    // 1. Tạo API key
    console.log("1️⃣ Tạo API key...");
    const apiKey = client.generateApiKey();
    console.log("✅ API Key:", apiKey.substring(0, 20) + "...");

    // 2. Thử kết nối
    console.log("\n2️⃣ Đang kết nối đến MCP server...");
    await client.connect(apiKey);
    console.log("✅ Kết nối thành công!");

    // 3. Kiểm tra trạng thái
    console.log("\n3️⃣ Kiểm tra trạng thái kết nối...");
    const isConnected = client.isClientConnected();
    console.log("✅ Trạng thái kết nối:", isConnected ? "Đã kết nối" : "Chưa kết nối");

    // 4. Kiểm tra auth
    console.log("\n4️⃣ Kiểm tra authentication...");
    const authStatus = await client.checkAuthStatus();
    console.log("Auth Status:", authStatus.success ? "✅ OK" : "❌ Lỗi");
    if (!authStatus.success) {
      console.log("Lỗi auth:", authStatus.error);
    }

    // 5. Lấy danh sách tools
    console.log("\n5️⃣ Lấy danh sách tools...");
    const tools = await client.listTools();
    if (tools.success) {
      console.log("✅ Tools có sẵn:", tools.data.tools?.length || 0);
      if (tools.data.tools && tools.data.tools.length > 0) {
        console.log("📋 Một số tools:");
        tools.data.tools.slice(0, 5).forEach((tool: any, index: number) => {
          console.log(`   ${index + 1}. ${tool.name}`);
        });
      }
    } else {
      console.log("❌ Không thể lấy danh sách tools:", tools.error);
    }

    // 6. Lấy danh sách resources
    console.log("\n6️⃣ Lấy danh sách resources...");
    const resources = await client.listResources();
    if (resources.success) {
      console.log("✅ Resources có sẵn:", resources.data.resources?.length || 0);
      if (resources.data.resources && resources.data.resources.length > 0) {
        console.log("📚 Một số resources:");
        resources.data.resources.slice(0, 3).forEach((resource: any, index: number) => {
          console.log(`   ${index + 1}. ${resource.name || resource.uri}`);
        });
      }
    } else {
      console.log("❌ Không thể lấy danh sách resources:", resources.error);
    }

    // 7. Test một số utility functions
    console.log("\n7️⃣ Test utility functions...");
    
    // Test validate API key format
    const validation = await client.validateApiKeyFormat({
      api_key: "sk-test1234567890abcdef1234567890abcdef",
      provider: "OPENAI"
    });
    console.log("Validate API key:", validation.success ? "✅ OK" : "❌ Lỗi");

    // Test model summary
    const modelSummary = await client.getModelSummary();
    console.log("Model summary:", modelSummary.success ? "✅ OK" : "❌ Lỗi");

    // Test suggest model
    const suggestion = await client.suggestModelForTask({
      task_type: "text_generation",
      budget: "low"
    });
    console.log("Model suggestion:", suggestion.success ? "✅ OK" : "❌ Lỗi");

    console.log("\n🎉 Test hoàn thành thành công!");
    console.log("✅ Client hoạt động bình thường và có thể kết nối với MCP server.");

  } catch (error) {
    console.error("\n❌ Lỗi trong quá trình test:", error);
    
    if (error instanceof Error) {
      if (error.message.includes("ECONNREFUSED")) {
        console.log("\n💡 Gợi ý: MCP server có thể chưa được khởi động.");
        console.log("   Hãy chạy server trước khi test client:");
        console.log("   python src/server/redai_system/model/model_server.py");
      } else if (error.message.includes("404")) {
        console.log("\n💡 Gợi ý: Endpoint MCP có thể không đúng.");
        console.log("   Kiểm tra server có đang chạy tại http://127.0.0.1:8006/mcp");
      }
    }
  } finally {
    // Đóng kết nối
    console.log("\n🔌 Đóng kết nối...");
    await client.disconnect();
    console.log("✅ Đã đóng kết nối.");
  }
}

// Chạy test nếu file được chạy trực tiếp
if (require.main === module) {
  testConnection().catch(console.error);
}

export { testConnection };
