# Getting Started - RedAI MCP Client

Hướng dẫn nhanh để bắt đầu sử dụng RedAI MCP Client TypeScript.

## 📋 Yêu cầu hệ thống

- Node.js >= 16.0.0
- TypeScript >= 4.5.0
- RedAI Model MCP Server đang chạy

## 🚀 Cài đặt và chạy

### 1. Cài đặt dependencies

```bash
npm install
```

### 2. Khởi động MCP Server (nếu chưa chạy)

Trước khi sử dụng client, đảm bảo MCP server đang chạy:

```bash
# Trong terminal khác, chạy server
python src/server/redai_system/model/model_server.py
```

Server sẽ chạy tại: `http://127.0.0.1:8006/mcp`

### 3. Test kết nối

```bash
# Test kết nối cơ bản
npm test

# Hoặc
npm run test:connection
```

### 4. Ch<PERSON>y ví dụ demo

```bash
# Chạy ví dụ đầy đủ
npm run example
```

## 📁 Cấu trúc files

```
├── redai-mcp-client.ts     # Main client class
├── example-usage.ts        # Ví dụ sử dụng đầy đủ
├── test-client.ts          # Test kết nối cơ bản
├── CLIENT-README.md        # API documentation
├── GETTING-STARTED.md      # File này
└── package.json           # Dependencies và scripts
```

## 🔧 Scripts có sẵn

```bash
# Test kết nối
npm test
npm run test:connection

# Chạy ví dụ demo
npm run example

# Chạy client cũ (SSE)
npm start

# Build TypeScript
npm run build

# Development mode
npm run dev
```

## 💡 Sử dụng cơ bản

### Import và khởi tạo

```typescript
import { RedAIMCPClient } from "./redai-mcp-client.js";

const client = new RedAIMCPClient();
```

### Kết nối và sử dụng

```typescript
async function quickStart() {
  try {
    // 1. Tạo API key
    const apiKey = client.generateApiKey();
    
    // 2. Kết nối
    await client.connect(apiKey);
    
    // 3. Sử dụng API
    const tools = await client.listTools();
    console.log("Tools:", tools.data.tools?.length);
    
    // 4. Tạo API key LLM
    const newKey = await client.createKeyLLM({
      name: "My OpenAI Key",
      provider: "OPENAI", 
      apiKey: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
    });
    
    if (newKey.success) {
      console.log("✅ API key created successfully");
    }
    
  } finally {
    await client.disconnect();
  }
}
```

## 🔍 Troubleshooting

### Lỗi kết nối

**Lỗi**: `ECONNREFUSED`
```
❌ Lỗi: connect ECONNREFUSED 127.0.0.1:8006
```

**Giải pháp**:
1. Kiểm tra MCP server có đang chạy không
2. Chạy server: `python src/server/redai_system/model/model_server.py`
3. Đảm bảo server chạy tại port 8006

### Lỗi authentication

**Lỗi**: `401 Unauthorized`

**Giải pháp**:
1. Kiểm tra Bearer token
2. Sử dụng `client.checkAuthStatus()` để debug
3. Cập nhật token: `client.updateBearerToken(newToken)`

### Lỗi transport

**Lỗi**: `Transport connection failed`

**Giải pháp**:
- Client tự động fallback từ Streamable HTTP sang SSE
- Nếu cả hai đều thất bại, kiểm tra server configuration

### Lỗi TypeScript

**Lỗi**: Module resolution errors

**Giải pháp**:
```bash
# Rebuild
npm run build

# Hoặc chạy trực tiếp với ts-node
npx ts-node test-client.ts
```

## 📚 Tài liệu chi tiết

- [CLIENT-README.md](./CLIENT-README.md) - API documentation đầy đủ
- [example-usage.ts](./example-usage.ts) - Ví dụ sử dụng chi tiết
- [README.md](./README.md) - Thông tin về MCP server

## 🔗 Workflow thông dụng

### 1. Quản lý API Keys

```typescript
// Validate format trước
const validation = await client.validateApiKeyFormat({
  api_key: "sk-...",
  provider: "OPENAI"
});

// Tạo key nếu valid
if (validation.success) {
  const newKey = await client.createKeyLLM({
    name: "My Key",
    provider: "OPENAI",
    apiKey: "sk-..."
  });
}

// Reload models
await client.reloadModels(keyId);
```

### 2. Fine-tuning workflow

```typescript
// 1. Tạo dataset
const dataset = await client.createDataset({
  name: "My Dataset",
  description: "Training data for my model"
});

// 2. Tính chi phí
const cost = await client.calculateFineTuneCost({
  training_tokens: 50000,
  provider: "OPENAI"
});

// 3. Tạo job
const job = await client.createFineTuningJob({
  datasetId: dataset.data.result.id,
  baseModel: "gpt-3.5-turbo",
  suffix: "my-model-v1"
});
```

### 3. Model discovery

```typescript
// Lấy system models
const systemModels = await client.getSystemModels({
  provider: "OPENAI"
});

// Gợi ý model cho task
const suggestion = await client.suggestModelForTask({
  task_type: "text_generation",
  budget: "medium"
});

// Tổng quan models
const summary = await client.getModelSummary();
```

## 🎯 Next Steps

1. Đọc [CLIENT-README.md](./CLIENT-README.md) để hiểu đầy đủ API
2. Chạy [example-usage.ts](./example-usage.ts) để xem workflow hoàn chỉnh
3. Tùy chỉnh client theo nhu cầu của bạn
4. Tích hợp vào ứng dụng của bạn

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra server logs
2. Chạy `npm test` để debug kết nối
3. Xem error messages trong console
4. Đảm bảo tất cả dependencies đã được cài đặt

Happy coding! 🚀
