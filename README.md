# RedAI Model MCP Server

Server MCP cho RedAI Model Module API sử dụng FastMCP và OpenAPI Schema. Server này tự động tạo các MCP tools và resources từ `swagger.json`, cung cấp giao diện MCP cho tất cả các endpoint của Model Module API.

## Tính năng

- ✅ **Tự động tạo tools từ OpenAPI specification**
- 🔑 **Hỗ trợ Bearer token authentication từ client**
- 🚀 **Streamable HTTP transport với FastMCP**
- 🔧 **Tùy chỉnh route mapping cho các loại endpoint khác nhau**
- 📊 **Xử lý parameters, headers và request body tự động**
- 🤖 **Tools tùy chỉnh cho model management functionality**

## Cấu trúc API

### Key LLM (Quản lý API Keys)
- `POST /user/key-llm` - Tạo API key mới với model auto-discovery
- `GET /user/key-llm` - Lấy danh sách API keys với phân trang và filter
- `PATCH /user/key-llm/{id}` - <PERSON><PERSON><PERSON> nhật API key với model auto-discovery
- `DELETE /user/key-llm/{id}` - Xóa API key và clear model mappings
- `POST /user/key-llm/{id}/reload-models` - Reload models từ API key

### Models (Quản lý Models)
- `GET /user/models/user-models-by-keys/{keyllmId}` - Lấy models theo API key
- `GET /user/models/system-models` - Lấy danh sách system models
- `GET /user/models/fine-tune-datasets` - Lấy danh sách fine-tune models

### Data Fine Tune (Quản lý Datasets)
- `POST /user/data-fine-tune` - Tạo dataset mới với upload URLs
- `GET /user/data-fine-tune` - Lấy danh sách datasets với filter
- `GET /user/data-fine-tune/upload-url` - Lấy URL upload dataset
- `GET /user/data-fine-tune/{id}` - Lấy chi tiết dataset
- `PATCH /user/data-fine-tune/{id}` - Cập nhật dataset
- `DELETE /user/data-fine-tune/{id}` - Xóa dataset
- `PATCH /user/data-fine-tune/{id}/upload-url-success` - Cập nhật trạng thái upload

### Fine Tuning Jobs (Quản lý Fine-tuning)
- `POST /user/fine-tuning-jobs` - Tạo fine-tuning job mới

## Cấu hình

### Biến môi trường

```bash
# API Configuration
REDAI_MODEL_API_BASE_URL=https://api.redai.com

# Server Configuration
MODEL_HTTP_HOST=127.0.0.1
MODEL_HTTP_PORT=8006
MODEL_HTTP_PATH=/mcp
MODEL_TRANSPORT=streamable-http
```

### Authentication

Server sử dụng Bearer token authentication:
- Bearer token được truyền từ client khi kết nối
- Tự động xử lý authentication cho tất cả requests
- Sử dụng tool `update_bearer_token` để cấu hình authentication

## Cài đặt và chạy

### 1. Cài đặt dependencies

```bash
pip install fastmcp>=2.3.0 httpx
# hoặc
pip install -e .
```

### 2. Chạy server

```bash
# Chạy với Streamable HTTP transport (mặc định)
python src/server/redai_system/model/model_server.py

# Chạy với transport cụ thể
python src/server/redai_system/model/model_server.py streamable-http
python src/server/redai_system/model/model_server.py sse
python src/server/redai_system/model/model_server.py stdio
```

Server sẽ khởi động tại:
- **MCP Endpoint**: `http://127.0.0.1:8006/mcp`
- **Server URL**: `http://127.0.0.1:8006`

## Sử dụng

### Kết nối từ MCP Client

```python
from fastmcp import Client

async def main():
    # Kết nối qua HTTP
    async with Client("http://127.0.0.1:8006/mcp") as client:
        # Cập nhật authentication
        await client.call_tool("update_bearer_token", {
            "bearer_token": "your_bearer_token_here"
        })
        
        # Tạo API key mới
        api_key = await client.call_tool("post_user_key_llm", {
            "name": "My OpenAI Key",
            "provider": "OPENAI",
            "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
        })
        
        # Lấy danh sách API keys
        keys = await client.call_tool("get_user_key_llm", {
            "page": 1,
            "limit": 10,
            "provider": "OPENAI"
        })
        
        # Tạo dataset fine-tune
        dataset = await client.call_tool("post_user_data_fine_tune", {
            "name": "Customer Service Dataset",
            "description": "Dataset for customer service fine-tuning",
            "trainFileUrl": "https://example.com/train.jsonl",
            "validFileUrl": "https://example.com/valid.jsonl"
        })
```

### Tools có sẵn

#### Authentication Tools
- `update_bearer_token` - Cập nhật Bearer token cho authentication
- `check_auth_status` - Kiểm tra trạng thái authentication hiện tại

#### Model Tools
- `get_model_summary` - Lấy tổng quan về models của người dùng
- `calculate_fine_tune_cost` - Tính toán chi phí fine-tuning dự kiến
- `suggest_model_for_task` - Gợi ý model phù hợp cho loại task cụ thể
- `validate_api_key_format` - Kiểm tra format của API key trước khi tạo

#### API Tools (tự động tạo từ OpenAPI)

**Key LLM Management:**
- `post_user_key_llm` - Tạo API key mới
- `get_user_key_llm` - Lấy danh sách API keys
- `patch_user_key_llm_id` - Cập nhật API key
- `delete_user_key_llm_id` - Xóa API key
- `post_user_key_llm_id_reload_models` - Reload models

**Models Management:**
- `get_user_models_user_models_by_keys_keyllmId` - Lấy models theo key
- `get_user_models_system_models` - Lấy system models
- `get_user_models_fine_tune_datasets` - Lấy fine-tune models

**Dataset Management:**
- `post_user_data_fine_tune` - Tạo dataset
- `get_user_data_fine_tune` - Lấy danh sách datasets
- `get_user_data_fine_tune_upload_url` - Lấy URL upload
- `get_user_data_fine_tune_id` - Chi tiết dataset
- `patch_user_data_fine_tune_id` - Cập nhật dataset
- `delete_user_data_fine_tune_id` - Xóa dataset
- `patch_user_data_fine_tune_id_upload_url_success` - Cập nhật trạng thái

**Fine-tuning Jobs:**
- `post_user_fine_tuning_jobs` - Tạo fine-tuning job

### Ví dụ sử dụng tools

```python
# Tính toán chi phí fine-tuning
cost = await client.call_tool("calculate_fine_tune_cost", {
    "training_tokens": 100000,
    "provider": "OPENAI"
})

# Gợi ý model cho task
suggestion = await client.call_tool("suggest_model_for_task", {
    "task_type": "code_generation",
    "budget": "medium"
})

# Validate API key format
validation = await client.call_tool("validate_api_key_format", {
    "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "provider": "OPENAI"
})

# Lấy tổng quan models
summary = await client.call_tool("get_model_summary", {})
```

## Workflow sử dụng

### 1. Quản lý API Keys

```python
# 1. Validate API key format trước khi tạo
validation = await client.call_tool("validate_api_key_format", {
    "api_key": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
    "provider": "OPENAI"
})

# 2. Tạo API key mới (tự động discovery models)
api_key = await client.call_tool("post_user_key_llm", {
    "name": "My OpenAI Key",
    "provider": "OPENAI",
    "apiKey": "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
})

# 3. Xem models được discovery
models = await client.call_tool("get_user_models_user_models_by_keys_keyllmId", {
    "keyllmId": api_key["result"]["id"],
    "page": 1,
    "limit": 20
})

# 4. Reload models nếu cần
reload = await client.call_tool("post_user_key_llm_id_reload_models", {
    "id": api_key["result"]["id"]
})
```

### 2. Quản lý Datasets và Fine-tuning

```python
# 1. Tạo dataset mới
dataset = await client.call_tool("post_user_data_fine_tune", {
    "name": "Customer Service Dataset",
    "description": "Dataset for customer service fine-tuning",
    "trainFileUrl": "https://example.com/train.jsonl",
    "validFileUrl": "https://example.com/valid.jsonl"
})

# 2. Upload files sử dụng URLs từ response
# (Sử dụng trainUploadUrl và validUploadUrl)

# 3. Cập nhật trạng thái sau khi upload
await client.call_tool("patch_user_data_fine_tune_id_upload_url_success", {
    "id": dataset["result"]["id"]
})

# 4. Tính toán chi phí fine-tuning
cost = await client.call_tool("calculate_fine_tune_cost", {
    "training_tokens": 50000,
    "provider": "OPENAI"
})

# 5. Tạo fine-tuning job
job = await client.call_tool("post_user_fine_tuning_jobs", {
    "datasetId": dataset["result"]["id"],
    "baseModel": "gpt-3.5-turbo",
    "suffix": "customer-service-v1",
    "hyperparameters": {
        "n_epochs": 3,
        "batch_size": 1,
        "learning_rate_multiplier": 0.1
    }
})
```

### 3. Khám phá Models

```python
# Xem system models có sẵn
system_models = await client.call_tool("get_user_models_system_models", {
    "provider": "OPENAI",
    "page": 1,
    "limit": 20
})

# Xem fine-tune models của user
fine_tune_models = await client.call_tool("get_user_models_fine_tune_datasets", {
    "page": 1,
    "limit": 10
})

# Gợi ý model cho task cụ thể
suggestion = await client.call_tool("suggest_model_for_task", {
    "task_type": "text_generation",
    "budget": "high"
})
```

## Cấu hình MCP Client

### Claude Desktop

```json
{
  "mcpServers": {
    "redai-model": {
      "command": "python",
      "args": ["src/server/redai_system/model/model_server.py"],
      "env": {
        "REDAI_MODEL_API_BASE_URL": "https://api.redai.com",
        "MODEL_HTTP_HOST": "127.0.0.1",
        "MODEL_HTTP_PORT": "8006"
      }
    }
  }
}
```

### Codeium/Continue

```json
{
  "mcpServers": {
    "redai-model": {
      "url": "http://127.0.0.1:8006/mcp"
    }
  }
}
```

## Troubleshooting

### Lỗi thường gặp

**Lỗi**: `FileNotFoundError: Không tìm thấy file schema tại: swagger.json`
- **Giải pháp**: Đảm bảo file `swagger.json` tồn tại trong thư mục `model/`

**Lỗi**: `ImportError: No module named 'fastmcp'`
- **Giải pháp**: Cài đặt dependencies: `pip install fastmcp>=2.3.0`

**Lỗi**: `ConnectionError` khi gọi API
- **Giải pháp**: Kiểm tra Bearer token và API base URL

**Lỗi**: `400 Bad Request` khi tạo API key
- **Giải pháp**: Kiểm tra format API key bằng tool `validate_api_key_format`

**Lỗi**: `Insufficient R-Points` khi tạo fine-tuning job
- **Giải pháp**: Kiểm tra số dư R-Points và tính toán chi phí trước

## Tài liệu tham khảo

- [FastMCP Documentation](https://github.com/jlowin/fastmcp)
- [Model Context Protocol](https://modelcontextprotocol.io)
- [OpenAPI Specification](https://swagger.io/specification/)
- [OpenAI Fine-tuning Guide](https://platform.openai.com/docs/guides/fine-tuning)
- [Google AI Fine-tuning Guide](https://ai.google.dev/docs/model_tuning_guidance)
