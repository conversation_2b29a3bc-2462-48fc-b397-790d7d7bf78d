import { Client } from "@modelcontextprotocol/sdk/client/index.js";
import { StreamableHTTPClientTransport } from "@modelcontextprotocol/sdk/client/streamableHttp.js";
import { SSEClientTransport } from "@modelcontextprotocol/sdk/client/sse.js";
import * as crypto from "crypto";

/**
 * RedAI MCP Client - Client TypeScript để kết nối với RedAI Model MCP Server
 * 
 * Client này cung cấp interface để tương tác với RedAI Model API thông qua MCP protocol.
 * Hỗ trợ cả Streamable HTTP transport (mới) và SSE transport (legacy) để đảm bảo tương thích ngược.
 */

// Cấu hình API
const API_CONFIG = {
  secretKey: "80fea_bd9a_17aa_add7_b7b4_2f59_c972_ede0",
  prefixKey: "redai",
  serverUrl: "http://127.0.0.1:8006/mcp",
  defaultAgentId: "fb838815-f1dd-4e57-83e6-bb635b39e236",
  defaultUserId: 1
};

/**
 * Interface cho kết quả API response
 */
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

/**
 * Interface cho API Key LLM
 */
interface KeyLLM {
  id: string;
  name: string;
  provider: string;
  apiKey: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho Dataset Fine-tune
 */
interface DataFineTune {
  id: string;
  name: string;
  description: string;
  trainFileUrl?: string;
  validFileUrl?: string;
  trainUploadUrl?: string;
  validUploadUrl?: string;
  status: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Interface cho Model
 */
interface Model {
  id: string;
  name: string;
  provider: string;
  type: string;
  capabilities: string[];
  pricing?: {
    input: number;
    output: number;
  };
}

/**
 * RedAI MCP Client Class
 */
export class RedAIMCPClient {
  private client: Client;
  private transport: StreamableHTTPClientTransport | SSEClientTransport | null = null;
  private bearerToken: string | null = null;
  private isConnected: boolean = false;

  constructor() {
    this.client = new Client({
      name: "redai-mcp-client",
      version: "1.0.0"
    });
  }

  /**
   * Tạo key mã hóa từ secret key
   */
  private generateKey(): Buffer {
    return crypto.createHash("sha256").update(API_CONFIG.secretKey).digest();
  }

  /**
   * Tạo initialization vector ngẫu nhiên
   */
  private generateIv(): Buffer {
    return crypto.randomBytes(16);
  }

  /**
   * Tạo API Key từ Agent ID và User ID
   */
  public generateApiKey(
    agentId: string = API_CONFIG.defaultAgentId,
    userId: number = API_CONFIG.defaultUserId
  ): string {
    try {
      const data = JSON.stringify({ agentId, userId });
      const key = this.generateKey();
      const iv = this.generateIv();
      const cipher = crypto.createCipheriv("aes-256-cbc", key, iv);
      let encrypted = cipher.update(data, "utf8", "base64");
      encrypted += cipher.final("base64");
      const result = iv.toString("base64") + ":" + encrypted;
      return `${API_CONFIG.prefixKey}_${result}`;
    } catch (error) {
      throw new Error("Không thể tạo API Key: " + (error as Error).message);
    }
  }

  /**
   * Kết nối đến MCP server với Streamable HTTP transport (ưu tiên) hoặc SSE transport (fallback)
   */
  public async connect(bearerToken?: string): Promise<void> {
    if (bearerToken) {
      this.bearerToken = bearerToken;
    }

    const baseUrl = new URL(API_CONFIG.serverUrl);
    
    try {
      // Thử kết nối với Streamable HTTP transport trước
      console.log("Đang thử kết nối với Streamable HTTP transport...");
      this.transport = new StreamableHTTPClientTransport(baseUrl);
      await this.client.connect(this.transport);
      console.log("✅ Kết nối thành công với Streamable HTTP transport");
      this.isConnected = true;
    } catch (error) {
      console.log("❌ Streamable HTTP transport thất bại, thử SSE transport...");
      
      try {
        // Fallback sang SSE transport cho tương thích ngược
        const sseUrl = new URL(baseUrl.origin + "/sse");
        this.transport = new SSEClientTransport(sseUrl);
        await this.client.connect(this.transport);
        console.log("✅ Kết nối thành công với SSE transport (legacy)");
        this.isConnected = true;
      } catch (sseError) {
        throw new Error(`Không thể kết nối với cả hai transport: ${error}, ${sseError}`);
      }
    }

    // Cập nhật Bearer token nếu có
    if (this.bearerToken) {
      await this.updateBearerToken(this.bearerToken);
    }
  }

  /**
   * Kiểm tra trạng thái kết nối
   */
  public isClientConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Đóng kết nối
   */
  public async disconnect(): Promise<void> {
    if (this.transport) {
      await this.transport.close();
      this.isConnected = false;
      console.log("🔌 Đã đóng kết nối MCP");
    }
  }

  /**
   * Cập nhật Bearer token cho authentication
   */
  public async updateBearerToken(token: string): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "update_bearer_token",
        arguments: {
          bearer_token: token
        }
      });
      
      this.bearerToken = token;
      return {
        success: true,
        data: result,
        message: "Bearer token đã được cập nhật thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Kiểm tra trạng thái authentication
   */
  public async checkAuthStatus(): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "check_auth_status",
        arguments: {}
      });
      
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách tất cả tools có sẵn
   */
  public async listTools(): Promise<ApiResponse> {
    try {
      const result = await this.client.listTools();
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách resources có sẵn
   */
  public async listResources(): Promise<ApiResponse> {
    try {
      const result = await this.client.listResources();
      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  // ==================== KEY LLM MANAGEMENT ====================

  /**
   * Tạo API key LLM mới
   */
  public async createKeyLLM(params: {
    name: string;
    provider: string;
    apiKey: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "post_user_key_llm",
        arguments: params
      });

      return {
        success: true,
        data: result,
        message: "API key đã được tạo thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách API keys với phân trang và filter
   */
  public async getKeyLLMList(params?: {
    page?: number;
    limit?: number;
    provider?: string;
    status?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_key_llm",
        arguments: params || {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Cập nhật API key
   */
  public async updateKeyLLM(id: string, params: {
    name?: string;
    apiKey?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "patch_user_key_llm_id",
        arguments: { id, ...params }
      });

      return {
        success: true,
        data: result,
        message: "API key đã được cập nhật thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Xóa API key
   */
  public async deleteKeyLLM(id: string): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "delete_user_key_llm_id",
        arguments: { id }
      });
      
      return {
        success: true,
        data: result,
        message: "API key đã được xóa thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Reload models từ API key
   */
  public async reloadModels(keyId: string): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "post_user_key_llm_id_reload_models",
        arguments: { id: keyId }
      });

      return {
        success: true,
        data: result,
        message: "Models đã được reload thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  // ==================== MODELS MANAGEMENT ====================

  /**
   * Lấy models theo API key
   */
  public async getModelsByKey(keyId: string, params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_models_user_models_by_keys_keyllmId",
        arguments: { keyllmId: keyId, ...params }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách system models
   */
  public async getSystemModels(params?: {
    provider?: string;
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_models_system_models",
        arguments: params || {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách fine-tune models
   */
  public async getFineTuneModels(params?: {
    page?: number;
    limit?: number;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_models_fine_tune_datasets",
        arguments: params || {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  // ==================== DATASET MANAGEMENT ====================

  /**
   * Tạo dataset fine-tune mới
   */
  public async createDataset(params: {
    name: string;
    description: string;
    trainFileUrl?: string;
    validFileUrl?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "post_user_data_fine_tune",
        arguments: params
      });

      return {
        success: true,
        data: result,
        message: "Dataset đã được tạo thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy danh sách datasets với filter
   */
  public async getDatasets(params?: {
    page?: number;
    limit?: number;
    status?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_data_fine_tune",
        arguments: params || {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy URL upload dataset
   */
  public async getUploadUrl(): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "get_user_data_fine_tune_upload_url",
        arguments: {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Lấy chi tiết dataset
   */
  public async getDatasetDetail(id: string): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "get_user_data_fine_tune_id",
        arguments: { id }
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Cập nhật dataset
   */
  public async updateDataset(id: string, params: {
    name?: string;
    description?: string;
  }): Promise<ApiResponse<any>> {
    try {
      const result = await this.client.callTool({
        name: "patch_user_data_fine_tune_id",
        arguments: { id, ...params }
      });

      return {
        success: true,
        data: result,
        message: "Dataset đã được cập nhật thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Xóa dataset
   */
  public async deleteDataset(id: string): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "delete_user_data_fine_tune_id",
        arguments: { id }
      });

      return {
        success: true,
        data: result,
        message: "Dataset đã được xóa thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Cập nhật trạng thái upload thành công
   */
  public async markUploadSuccess(id: string): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "patch_user_data_fine_tune_id_upload_url_success",
        arguments: { id }
      });

      return {
        success: true,
        data: result,
        message: "Trạng thái upload đã được cập nhật"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  // ==================== FINE-TUNING JOBS ====================

  /**
   * Tạo fine-tuning job mới
   */
  public async createFineTuningJob(params: {
    datasetId: string;
    baseModel: string;
    suffix?: string;
    hyperparameters?: {
      n_epochs?: number;
      batch_size?: number;
      learning_rate_multiplier?: number;
    };
  }): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "post_user_fine_tuning_jobs",
        arguments: params
      });

      return {
        success: true,
        data: result,
        message: "Fine-tuning job đã được tạo thành công"
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  // ==================== UTILITY TOOLS ====================

  /**
   * Lấy tổng quan về models của người dùng
   */
  public async getModelSummary(): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "get_model_summary",
        arguments: {}
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Tính toán chi phí fine-tuning dự kiến
   */
  public async calculateFineTuneCost(params: {
    training_tokens: number;
    provider: string;
  }): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "calculate_fine_tune_cost",
        arguments: params
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Gợi ý model phù hợp cho loại task cụ thể
   */
  public async suggestModelForTask(params: {
    task_type: string;
    budget?: string;
  }): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "suggest_model_for_task",
        arguments: params
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * Kiểm tra format của API key trước khi tạo
   */
  public async validateApiKeyFormat(params: {
    api_key: string;
    provider: string;
  }): Promise<ApiResponse> {
    try {
      const result = await this.client.callTool({
        name: "validate_api_key_format",
        arguments: params
      });

      return {
        success: true,
        data: result
      };
    } catch (error) {
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }
}
