# RedAI MCP Client - TypeScript

Client TypeScript để kết nối và tương tác với RedAI Model MCP Server thông qua Model Context Protocol (MCP).

## Tính năng

- ✅ **Kết nối tự động với Streamable HTTP và SSE transport** (tương thích ngược)
- 🔑 **Quản lý API Keys LLM** (tạo, cập nhật, xóa, reload models)
- 🤖 **Quản lý Models** (system models, user models, fine-tune models)
- 📊 **Quản lý Datasets** (tạo, cập nhật, xóa, upload)
- 🎯 **Fine-tuning Jobs** (tạo và quản lý fine-tuning jobs)
- 💰 **Tính toán chi phí** và gợi ý models
- 🔐 **Authentication tự động** với Bearer token
- 🛠️ **TypeScript support** với type safety

## Cài đặt

```bash
# Cài đặt dependencies
npm install

# Hoặc với yarn
yarn install
```

## Dependencies

```json
{
  "@modelcontextprotocol/sdk": "^1.12.0",
  "crypto": "^1.0.1",
  "zod": "^3.22.4"
}
```

## Sử dụng cơ bản

### 1. Import và khởi tạo client

```typescript
import { RedAIMCPClient } from "./redai-mcp-client.js";

const client = new RedAIMCPClient();
```

### 2. Tạo API key và kết nối

```typescript
// Tạo API key cho authentication
const apiKey = client.generateApiKey();

// Kết nối đến MCP server
await client.connect(apiKey);
```

### 3. Sử dụng các phương thức API

```typescript
// Kiểm tra trạng thái authentication
const authStatus = await client.checkAuthStatus();

// Lấy danh sách tools
const tools = await client.listTools();

// Tạo API key LLM mới
const newKey = await client.createKeyLLM({
  name: "My OpenAI Key",
  provider: "OPENAI",
  apiKey: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
});
```

## API Reference

### Authentication

```typescript
// Tạo API key từ agentId và userId
generateApiKey(agentId?: string, userId?: number): string

// Kết nối đến server
connect(bearerToken?: string): Promise<void>

// Cập nhật Bearer token
updateBearerToken(token: string): Promise<ApiResponse>

// Kiểm tra trạng thái auth
checkAuthStatus(): Promise<ApiResponse>

// Đóng kết nối
disconnect(): Promise<void>
```

### Key LLM Management

```typescript
// Tạo API key mới
createKeyLLM(params: {
  name: string;
  provider: string;
  apiKey: string;
}): Promise<ApiResponse>

// Lấy danh sách API keys
getKeyLLMList(params?: {
  page?: number;
  limit?: number;
  provider?: string;
  status?: string;
}): Promise<ApiResponse>

// Cập nhật API key
updateKeyLLM(id: string, params: {
  name?: string;
  apiKey?: string;
}): Promise<ApiResponse>

// Xóa API key
deleteKeyLLM(id: string): Promise<ApiResponse>

// Reload models từ API key
reloadModels(keyId: string): Promise<ApiResponse>
```

### Models Management

```typescript
// Lấy models theo API key
getModelsByKey(keyId: string, params?: {
  page?: number;
  limit?: number;
}): Promise<ApiResponse>

// Lấy system models
getSystemModels(params?: {
  provider?: string;
  page?: number;
  limit?: number;
}): Promise<ApiResponse>

// Lấy fine-tune models
getFineTuneModels(params?: {
  page?: number;
  limit?: number;
}): Promise<ApiResponse>
```

### Dataset Management

```typescript
// Tạo dataset mới
createDataset(params: {
  name: string;
  description: string;
  trainFileUrl?: string;
  validFileUrl?: string;
}): Promise<ApiResponse>

// Lấy danh sách datasets
getDatasets(params?: {
  page?: number;
  limit?: number;
  status?: string;
}): Promise<ApiResponse>

// Lấy chi tiết dataset
getDatasetDetail(id: string): Promise<ApiResponse>

// Cập nhật dataset
updateDataset(id: string, params: {
  name?: string;
  description?: string;
}): Promise<ApiResponse>

// Xóa dataset
deleteDataset(id: string): Promise<ApiResponse>

// Lấy URL upload
getUploadUrl(): Promise<ApiResponse>

// Đánh dấu upload thành công
markUploadSuccess(id: string): Promise<ApiResponse>
```

### Fine-tuning Jobs

```typescript
// Tạo fine-tuning job
createFineTuningJob(params: {
  datasetId: string;
  baseModel: string;
  suffix?: string;
  hyperparameters?: {
    n_epochs?: number;
    batch_size?: number;
    learning_rate_multiplier?: number;
  };
}): Promise<ApiResponse>
```

### Utility Tools

```typescript
// Lấy tổng quan models
getModelSummary(): Promise<ApiResponse>

// Tính toán chi phí fine-tuning
calculateFineTuneCost(params: {
  training_tokens: number;
  provider: string;
}): Promise<ApiResponse>

// Gợi ý model cho task
suggestModelForTask(params: {
  task_type: string;
  budget?: string;
}): Promise<ApiResponse>

// Validate API key format
validateApiKeyFormat(params: {
  api_key: string;
  provider: string;
}): Promise<ApiResponse>
```

## Ví dụ sử dụng

### Workflow hoàn chỉnh

```typescript
import { RedAIMCPClient } from "./redai-mcp-client.js";

async function example() {
  const client = new RedAIMCPClient();
  
  try {
    // 1. Kết nối
    const apiKey = client.generateApiKey();
    await client.connect(apiKey);
    
    // 2. Validate và tạo API key
    const validation = await client.validateApiKeyFormat({
      api_key: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
      provider: "OPENAI"
    });
    
    if (validation.success) {
      const newKey = await client.createKeyLLM({
        name: "My OpenAI Key",
        provider: "OPENAI",
        apiKey: "sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
      });
      
      if (newKey.success) {
        // 3. Reload models
        await client.reloadModels(newKey.data.result.id);
        
        // 4. Tạo dataset
        const dataset = await client.createDataset({
          name: "Customer Service Dataset",
          description: "Dataset for fine-tuning customer service model"
        });
        
        if (dataset.success) {
          // 5. Tính chi phí
          const cost = await client.calculateFineTuneCost({
            training_tokens: 50000,
            provider: "OPENAI"
          });
          
          // 6. Tạo fine-tuning job
          const job = await client.createFineTuningJob({
            datasetId: dataset.data.result.id,
            baseModel: "gpt-3.5-turbo",
            suffix: "customer-service-v1"
          });
        }
      }
    }
  } finally {
    await client.disconnect();
  }
}
```

## Chạy ví dụ

```bash
# Chạy ví dụ demo
npm run start:new

# Hoặc
ts-node example-usage.ts
```

## Cấu hình

Client sử dụng các cấu hình mặc định:

```typescript
const API_CONFIG = {
  secretKey: "80fea_bd9a_17aa_add7_b7b4_2f59_c972_ede0",
  prefixKey: "redai",
  serverUrl: "http://127.0.0.1:8006/mcp",
  defaultAgentId: "fb838815-f1dd-4e57-83e6-bb635b39e236",
  defaultUserId: 1
};
```

## Transport Support

Client hỗ trợ cả hai loại transport:

1. **Streamable HTTP** (ưu tiên) - Transport mới, hiệu suất cao
2. **SSE (Server-Sent Events)** (fallback) - Transport legacy cho tương thích ngược

Client sẽ tự động thử Streamable HTTP trước, nếu thất bại sẽ fallback sang SSE.

## Error Handling

Tất cả phương thức trả về `ApiResponse<T>` với cấu trúc:

```typescript
interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
```

## Troubleshooting

### Lỗi kết nối
- Đảm bảo MCP server đang chạy tại `http://127.0.0.1:8006/mcp`
- Kiểm tra Bearer token hợp lệ

### Lỗi authentication
- Sử dụng `checkAuthStatus()` để kiểm tra trạng thái
- Cập nhật Bearer token bằng `updateBearerToken()`

### Lỗi API calls
- Kiểm tra `result.success` trước khi sử dụng `result.data`
- Log `result.error` để debug

## License

MIT License
